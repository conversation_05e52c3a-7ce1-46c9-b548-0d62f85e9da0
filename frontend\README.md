# Happi 巧克力品牌演示网站

这是一个静态的 HTML 网站项目，展示 Happi 巧克力品牌的产品和信息。

## 项目结构

```
choco/
├── frontend/                 # 前端文件夹
│   ├── assets/              # 静态资源
│   │   ├── css/            # 样式文件
│   │   └── js/             # JavaScript 文件
│   ├── index.html          # 首页
│   ├── products.html       # 产品页面
│   ├── story.html          # 品牌故事页面
│   ├── benefits.html       # 优势页面
│   └── subscribe.html      # 订阅页面
├── node_modules/           # 依赖包
├── package.json           # 项目配置文件
├── package-lock.json      # 依赖锁定文件
└── README.md             # 项目说明文件
```

## 安装和运行

### 前提条件

- Node.js (>= 14.0.0)
- npm (>= 6.0.0)

### 安装依赖

```bash
npm install
```

### 运行项目

#### 启动开发服务器

```bash
npm run serve
# 或者
npm start
```

服务器将在 `http://localhost:8080` 启动

#### 启动开发服务器并自动打开浏览器

```bash
npm run dev
```

### 可用的脚本命令

- `npm run serve` - 启动 HTTP 服务器，服务 frontend 文件夹
- `npm start` - 同 `npm run serve`
- `npm run dev` - 启动服务器并自动打开浏览器
- `npm run build` - 显示构建信息（静态站点无需构建）
- `npm run clean` - 清理 node_modules 和 package-lock.json
- `npm test` - 显示测试信息

## 功能特性

- 响应式设计，支持移动端和桌面端
- 现代化的 CSS 样式
- 交互式动画效果
- 多页面导航
- 产品展示卡片
- 订阅表单（演示用）

## 页面说明

- **首页 (index.html)** - 品牌介绍和主要产品展示
- **商店 (products.html)** - 产品列表和详情
- **关于我们 (story.html)** - 品牌故事和历史
- **优势 (benefits.html)** - 产品优势和特点
- **订阅 (subscribe.html)** - 邮件订阅页面

## 技术栈

- HTML5
- CSS3
- JavaScript (ES6+)
- http-server (开发服务器)

## 浏览器支持

- Chrome (最新版本)
- Firefox (最新版本)
- Safari (最新版本)
- Edge (最新版本)

## 开发说明

这是一个纯静态网站项目，所有文件都位于 `frontend` 文件夹中。使用 `http-server` 作为开发服务器，支持 CORS 和热重载。

## 许可证

MIT License
