.TH http-server 1 "April 2020" GNU "http-server man page"

.SH NAME
http-server \- a simple zero-configuration command-line http server

.SH SYNOPSIS
.B http-server
[\fIPATH\fR]
[\fIOPTIONS\fR]

.SH DESCRIPTION
\fBhttp-server\fR is a simple, zero-configuration command-line http server. It is powerful enough for production usage, but it's simple and hackable enough to be used for testing, local development, and learning.

.SH OPTIONS

.TP
.BI [\fIPATH\fR]
The directory to serve.
Defaults to ./public if it exists, and ./ otherwise.

.TP
.BI \-p ", " \-\-port " " \fIPORT\fR
Port to use. If 0, look for the first available port, starting at 8080.
Default is 8080.

.TP
.BI \-a " " \fIADDRESS\fR
Address to use.
Default is 0.0.0.0.

.TP
.BI \-d
Show directory listings.
Default is true.

.TP
.BI \-i
Display autoIndex.
Default is true.

.TP
.BI \-g ", " \-\-gzip
Serve gzip files when possible.
Default is false.

.TP
.BI \-b ", " \-\-brotli
Serve brotli files when possible.
If both brotli and gzip are enabled, brotli takes precedence.
Default is false.

.TP
.BI \-e ", " \-\-ext " " \fIEXTENSION\fR
Default file extension is none is provided.

.TP
.BI \-s ", " \-\-silent
Suppress log messages from output.

.TP
.BI \-\-cors " " [\fIHEADERS\fR]
Enable CORS via the "Access-Control-Allow-Origin" header.
Optionally provide CORS headers list separated by commas.

.TP
.BI \-o " " [\fIPATH\fR]
Open default browser window after starting the server.
Optionally provide a URL path to open the browser window to.

.TP
.BI \-c " " \fITIME\fR
Cache time (max-age) in seconds.
To disable caching, use \-c \-1.
Default is 3600.

.TP
.BI \-U ", " \-\-utc
Use UTC time format in log messages.

.TP
.BI \-\-log\-ip
Enable logging of the client IP address.

.TP
.BI \-P ", " \-\-proxy
Fallback proxy if the request cannot be resolved.

.TP
.BI \-\-proxy\-options
Pass proxy options using nested dotted objects.

.TP
.BI \-\-username " " \fIUSERNAME\fR
Username for basic authentication.
Can also be specified with the environment variable NODE_HTTP_SERVER_USERNAME.
Defaults to none.

.TP
.BI \-\-password " " \fIPASSWORD\fR
Password for basic authentication.
Can also be specified with the environment variable NODE_HTTP_SERVER_PASSWORD.
Defaults to none.

.TP
.BI \-S ", " \-\-tls ", " \-\-ssl
Enable https.

.TP
.BI \-C ", " \-\-cert " " [\fIFILE\fR]
Path to SSL certificate file.
If not specified, uses cert.pem.

.TP
.BI \-K ", " \-\-key " " [\fIFILE\fR]
Path to SSL key file.
If not specified, uses key.pem.
Passphrase will be read from NODE_HTTP_SERVER_SSL_PASSPHRASE (if set)

.TP
.BI \-r ", " \-\-robots " " [\fIUSER\-AGENT\fR]
Respond to /robots.txt request.
If not specified, uses "User-agent: *\\nDisallow: /]"

.TP
.BI \-\-no\-dotfiles
Do not show dotfiles.

.TP
.BI \-h ", " \-\-help
Print usage and exit.

.TP
.BI \-v ", " \-\-version
Print version and exit.

.SH FILES
.B index.html
will be served as the default file to any directory requests.

.B 404.html
will be served if a file is not found. This can be used for SPA hosting to serve the entry page.

.SH COPYING
Copyright (c) 2011-2022 Charlie Robbins, Marak Squires, and the Contributors.
Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

.SH VERSION
Version 0.12.2
